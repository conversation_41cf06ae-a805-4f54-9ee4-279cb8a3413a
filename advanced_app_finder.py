import os
import subprocess
import glob
import winreg
from pathlib import Path

def get_shortcut_target(lnk_path):
    """<PERSON><PERSON><PERSON> đường dẫn thực tế từ file .lnk bằng PowerShell"""
    try:
        cmd = f'''powershell -command "(New-Object -COM WScript.Shell).CreateShortcut('{lnk_path}').TargetPath"'''
        result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            target = result.stdout.strip()
            if target and os.path.exists(target):
                return target
    except Exception as e:
        print(f"Lỗi khi đọc shortcut: {e}")
    return None

def find_app_in_registry(app_name):
    """Tìm ứng dụng trong Windows Registry"""
    try:
        # Tìm trong Uninstall registry
        reg_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        ]
        
        for reg_path in reg_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    if app_name.lower() in display_name.lower():
                                        try:
                                            install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                            if install_location and os.path.exists(install_location):
                                                print(f"Tìm thấy trong registry: {install_location}")
                                                return install_location
                                        except FileNotFoundError:
                                            pass
                                except FileNotFoundError:
                                    pass
                        except OSError:
                            continue
            except OSError:
                continue
    except Exception as e:
        print(f"Lỗi khi tìm trong registry: {e}")
    return None

def find_exe_files(directory):
    """Tìm tất cả file .exe trong thư mục và thư mục con"""
    exe_files = []
    if os.path.exists(directory):
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.exe'):
                    exe_files.append(os.path.join(root, file))
    return exe_files

def analyze_922_s5_proxy():
    """Phân tích chi tiết về 922 S5 Proxy"""
    print("🔍 PHÂN TÍCH CHI TIẾT 922 S5 PROXY")
    print("=" * 50)
    
    app_folder = r"C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy"
    
    # 1. Kiểm tra thư mục Start Menu
    print(f"\n📁 Kiểm tra thư mục: {app_folder}")
    if os.path.exists(app_folder):
        print("✅ Thư mục tồn tại")
        
        # Liệt kê tất cả file
        all_files = os.listdir(app_folder)
        print(f"📋 Các file trong thư mục:")
        for file in all_files:
            file_path = os.path.join(app_folder, file)
            file_size = os.path.getsize(file_path)
            print(f"   - {file} ({file_size} bytes)")
            
            # Nếu là file .lnk, thử đọc target
            if file.endswith('.lnk'):
                target = get_shortcut_target(file_path)
                if target:
                    print(f"     → Target: {target}")
                    return target
    else:
        print("❌ Thư mục không tồn tại")
    
    # 2. Tìm trong Registry
    print(f"\n🔍 Tìm trong Windows Registry...")
    registry_path = find_app_in_registry("922 S5 Proxy")
    if registry_path:
        exe_files = find_exe_files(registry_path)
        if exe_files:
            print(f"📋 File .exe tìm thấy:")
            for exe in exe_files:
                print(f"   - {exe}")
            return exe_files[0]
    
    # 3. Tìm trong Program Files
    print(f"\n🔍 Tìm trong Program Files...")
    program_dirs = [
        r"C:\Program Files",
        r"C:\Program Files (x86)",
        r"C:\Users\<USER>\AppData\Local".format(os.getenv('USERNAME')),
        r"C:\Users\<USER>\AppData\Roaming".format(os.getenv('USERNAME'))
    ]
    
    for prog_dir in program_dirs:
        if os.path.exists(prog_dir):
            for root, dirs, files in os.walk(prog_dir):
                if "922" in root.lower() or "s5" in root.lower() or "proxy" in root.lower():
                    print(f"📁 Tìm thấy thư mục liên quan: {root}")
                    exe_files = find_exe_files(root)
                    if exe_files:
                        print(f"📋 File .exe:")
                        for exe in exe_files:
                            print(f"   - {exe}")
                        return exe_files[0]
    
    return None

def try_multiple_launch_methods(app_path):
    """Thử nhiều phương pháp khác nhau để mở ứng dụng"""
    print(f"\n🚀 Thử mở ứng dụng: {app_path}")
    
    methods = [
        ("os.startfile()", lambda: os.startfile(app_path)),
        ("subprocess.Popen()", lambda: subprocess.Popen([app_path])),
        ("subprocess.run() với shell=True", lambda: subprocess.run([app_path], shell=True)),
        ("cmd start", lambda: subprocess.run(['cmd', '/c', 'start', '', f'"{app_path}"'], shell=True)),
        ("cmd start không quote", lambda: subprocess.run(['cmd', '/c', 'start', '', app_path], shell=True)),
    ]
    
    for method_name, method_func in methods:
        try:
            print(f"🔄 Thử phương pháp: {method_name}")
            method_func()
            print(f"✅ Thành công với {method_name}!")
            return True
        except Exception as e:
            print(f"❌ Lỗi với {method_name}: {e}")
    
    return False

def main():
    print("🔍 ADVANCED 922 S5 PROXY FINDER")
    print("=" * 40)
    
    # Phân tích và tìm đường dẫn thực tế
    real_path = analyze_922_s5_proxy()
    
    if real_path:
        print(f"\n✅ Tìm thấy đường dẫn thực tế: {real_path}")
        
        # Thử mở bằng nhiều phương pháp
        success = try_multiple_launch_methods(real_path)
        
        if not success:
            print("\n❌ Không thể mở ứng dụng bằng bất kỳ phương pháp nào")
            print("💡 Gợi ý:")
            print("   1. Thử chạy script với quyền Administrator")
            print("   2. Kiểm tra xem ứng dụng có bị block bởi antivirus không")
            print("   3. Thử mở thủ công để kiểm tra ứng dụng có hoạt động không")
    else:
        print("\n❌ Không tìm thấy đường dẫn thực tế của ứng dụng")
        print("💡 Hãy thử:")
        print("   1. Kiểm tra lại tên ứng dụng")
        print("   2. Tìm thủ công trong Start Menu")
        print("   3. Kiểm tra ứng dụng có được cài đặt đúng không")
    
    input("\nNhấn Enter để thoát...")

if __name__ == "__main__":
    main()

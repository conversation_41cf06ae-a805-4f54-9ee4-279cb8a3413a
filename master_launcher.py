import os
import subprocess
import time

# Đường dẫn chính xác của 922 S5 Proxy
APP_PATH = r"C:\Program Files (x86)\922_S5_Proxy\922_S5_Proxy.exe"
SHORTCUT_PATH = r"C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy\922 S5 Proxy.lnk"

def quick_launch():
    """Mở 922 S5 Proxy nhanh chóng"""
    print("🚀 Mở nhanh 922 S5 Proxy...")
    try:
        if os.path.exists(APP_PATH):
            os.startfile(APP_PATH)
            print("✅ Đã mở thành công!")
            return True
        else:
            print("❌ Không tìm thấy ứng dụng!")
            return False
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def launch_with_shortcut():
    """Mở bằng shortcut"""
    print("🔗 Mở bằng shortcut...")
    try:
        if os.path.exists(SHORTCUT_PATH):
            os.startfile(SHORTCUT_PATH)
            print("✅ Đã mở bằng shortcut!")
            return True
        else:
            print("❌ Không tìm thấy shortcut!")
            return False
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def launch_with_cmd():
    """Mở bằng command line"""
    print("💻 Mở bằng command line...")
    try:
        subprocess.run(['cmd', '/c', 'start', '', f'"{APP_PATH}"'], shell=True)
        print("✅ Đã thử mở bằng CMD!")
        return True
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def check_if_running():
    """Kiểm tra xem ứng dụng có đang chạy không"""
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq 922_S5_Proxy.exe'], 
                              capture_output=True, text=True)
        if "922_S5_Proxy.exe" in result.stdout:
            return True
        return False
    except:
        return False

def close_app():
    """Đóng ứng dụng"""
    print("❌ Đang đóng 922 S5 Proxy...")
    try:
        result = subprocess.run(['taskkill', '/F', '/IM', '922_S5_Proxy.exe'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Đã đóng thành công!")
            return True
        else:
            print("❌ Không thể đóng (có thể không đang chạy)")
            return False
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def show_app_info():
    """Hiển thị thông tin ứng dụng"""
    print("\n" + "="*50)
    print("📋 THÔNG TIN 922 S5 PROXY")
    print("="*50)
    
    print(f"📁 Đường dẫn chính: {APP_PATH}")
    print(f"   Tồn tại: {'✅ Có' if os.path.exists(APP_PATH) else '❌ Không'}")
    
    print(f"\n🔗 Shortcut: {SHORTCUT_PATH}")
    print(f"   Tồn tại: {'✅ Có' if os.path.exists(SHORTCUT_PATH) else '❌ Không'}")
    
    print(f"\n🔄 Trạng thái: {'✅ Đang chạy' if check_if_running() else '❌ Không chạy'}")

def main():
    print("🎯" + "="*60)
    print("    MASTER LAUNCHER - 922 S5 PROXY")
    print("🎯" + "="*60)
    
    while True:
        # Hiển thị trạng thái hiện tại
        status = "🟢 ĐANG CHẠY" if check_if_running() else "🔴 KHÔNG CHẠY"
        print(f"\n📊 Trạng thái hiện tại: {status}")
        
        print("\n📋 Tùy chọn:")
        print("1. ⚡ Mở nhanh (os.startfile)")
        print("2. 🔗 Mở bằng shortcut")
        print("3. 💻 Mở bằng command line")
        print("4. 🔄 Khởi động lại")
        print("5. ❌ Đóng ứng dụng")
        print("6. 📋 Xem thông tin")
        print("7. 🚪 Thoát")
        
        choice = input("\n👉 Chọn (1-7): ").strip()
        
        if choice == "1":
            print("\n" + "-"*30)
            quick_launch()
            
        elif choice == "2":
            print("\n" + "-"*30)
            launch_with_shortcut()
            
        elif choice == "3":
            print("\n" + "-"*30)
            launch_with_cmd()
            
        elif choice == "4":
            print("\n" + "-"*30)
            print("🔄 Khởi động lại...")
            if check_if_running():
                close_app()
                time.sleep(2)
            quick_launch()
            
        elif choice == "5":
            print("\n" + "-"*30)
            close_app()
            
        elif choice == "6":
            show_app_info()
            
        elif choice == "7":
            print("\n👋 Tạm biệt!")
            break
            
        else:
            print("❌ Lựa chọn không hợp lệ!")
        
        # Đợi một chút trước khi hiển thị menu lại
        time.sleep(1)

if __name__ == "__main__":
    main()

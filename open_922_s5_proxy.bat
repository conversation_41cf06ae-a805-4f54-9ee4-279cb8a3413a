@echo off
echo =================================
echo     MO 922 S5 PROXY
echo =================================

REM Duong dan chinh xac cua ung dung
set "APP_PATH=C:\Program Files (x86)\922_S5_Proxy\922_S5_Proxy.exe"

echo Dang mo 922 S5 Proxy...

REM Kiem tra file co ton tai khong
if exist "%APP_PATH%" (
    echo Tim thay ung dung: %APP_PATH%
    echo Dang mo...
    start "" "%APP_PATH%"
    echo 922 S5 Proxy da duoc mo thanh cong!
    goto :success
) else (
    echo Khong tim thay file: %APP_PATH%
    echo Thu phuong an du phong...

    REM Thu mo bang shortcut
    set "SHORTCUT_PATH=C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy\922 S5 Proxy.lnk"
    if exist "%SHORTCUT_PATH%" (
        start "" "%SHORTCUT_PATH%"
        echo Da thu mo bang shortcut!
    ) else (
        echo Khong tim thay shortcut, thu mo bang ten...
        start "" "922 S5 Proxy"
    )
)

:success
echo.
pause

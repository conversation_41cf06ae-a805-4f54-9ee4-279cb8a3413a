@echo off
echo =================================
echo     MO 922 S5 PROXY
echo =================================

REM Thu muc chua ung dung
set "APP_FOLDER=C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy"

echo Dang tim va mo 922 S5 Proxy...

REM Kiem tra xem thu muc co ton tai khong
if exist "%APP_FOLDER%" (
    echo Tim thay thu muc: %APP_FOLDER%
    
    REM Tim file .lnk dau tien
    for %%f in ("%APP_FOLDER%\*.lnk") do (
        echo Tim thay shortcut: %%f
        start "" "%%f"
        echo 922 S5 Proxy da duoc mo thanh cong!
        goto :success
    )
    
    REM Neu khong co .lnk, tim file .exe
    for %%f in ("%APP_FOLDER%\*.exe") do (
        echo Tim thay executable: %%f
        start "" "%%f"
        echo 922 S5 Proxy da duoc mo thanh cong!
        goto :success
    )
    
    echo Khong tim thay file thuc thi trong thu muc
) else (
    echo Khong tim thay thu muc: %APP_FOLDER%
)

REM Phuong an du phong: thu mo bang ten
echo Thu phuong an du phong...
start "" "922 S5 Proxy"
echo Da thu mo 922 S5 Proxy bang ten!

:success
echo.
pause

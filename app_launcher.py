import os
import subprocess
import sys
import time
from pathlib import Path

class AppLauncher:
    def __init__(self):
        self.common_paths = [
            r"C:\ProgramData\Microsoft\Windows\Start Menu\Programs",
            r"C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs".format(os.getenv('USERNAME')),
            r"C:\Users\<USER>\Desktop".format(os.getenv('USERNAME')),
            r"C:\Program Files",
            r"C:\Program Files (x86)"
        ]
    
    def find_app(self, app_name):
        """Tìm kiếm ứng dụng trong các thư mục phổ biến"""
        print(f"Đang tìm kiếm ứng dụng: {app_name}")
        
        for base_path in self.common_paths:
            if os.path.exists(base_path):
                for root, dirs, files in os.walk(base_path):
                    for file in files:
                        if app_name.lower() in file.lower() and (file.endswith('.exe') or file.endswith('.lnk')):
                            full_path = os.path.join(root, file)
                            print(f"Tìm thấy: {full_path}")
                            return full_path
        return None
    
    def launch_app_by_path(self, app_path):
        """Mở ứng dụng bằng đường dẫn cụ thể"""
        try:
            if os.path.exists(app_path):
                print(f"Đang mở ứng dụng: {app_path}")
                if app_path.endswith('.lnk'):
                    # Sử dụng start command cho shortcut
                    subprocess.run(['cmd', '/c', 'start', '', app_path], shell=True)
                else:
                    # Mở trực tiếp file exe
                    subprocess.Popen([app_path])
                print("Ứng dụng đã được mở thành công!")
                return True
            else:
                print(f"Không tìm thấy file: {app_path}")
                return False
        except Exception as e:
            print(f"Lỗi khi mở ứng dụng: {e}")
            return False
    
    def launch_app_by_name(self, app_name):
        """Mở ứng dụng bằng tên"""
        # Thử tìm kiếm ứng dụng
        app_path = self.find_app(app_name)
        if app_path:
            return self.launch_app_by_path(app_path)
        else:
            # Thử mở bằng command line
            try:
                print(f"Thử mở ứng dụng bằng command: {app_name}")
                subprocess.run(['cmd', '/c', 'start', app_name], shell=True)
                print("Ứng dụng đã được mở!")
                return True
            except Exception as e:
                print(f"Không thể mở ứng dụng: {e}")
                return False
    
    def launch_922_s5_proxy(self):
        """Mở ứng dụng 922 S5 Proxy cụ thể"""
        app_path = r"C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy"
        
        # Tìm file .lnk trong thư mục
        if os.path.exists(app_path):
            for file in os.listdir(app_path):
                if file.endswith('.lnk') or file.endswith('.exe'):
                    full_path = os.path.join(app_path, file)
                    return self.launch_app_by_path(full_path)
        
        # Nếu không tìm thấy, thử tìm kiếm
        return self.launch_app_by_name("922 S5 Proxy")

def main():
    launcher = AppLauncher()
    
    print("=== AUTO APP LAUNCHER ===")
    print("1. Mở 922 S5 Proxy")
    print("2. Mở ứng dụng khác bằng tên")
    print("3. Mở ứng dụng bằng đường dẫn")
    print("4. Thoát")
    
    while True:
        try:
            choice = input("\nChọn tùy chọn (1-4): ").strip()
            
            if choice == "1":
                print("\nĐang mở 922 S5 Proxy...")
                launcher.launch_922_s5_proxy()
                
            elif choice == "2":
                app_name = input("Nhập tên ứng dụng: ").strip()
                if app_name:
                    launcher.launch_app_by_name(app_name)
                else:
                    print("Vui lòng nhập tên ứng dụng!")
                    
            elif choice == "3":
                app_path = input("Nhập đường dẫn đầy đủ: ").strip()
                if app_path:
                    launcher.launch_app_by_path(app_path)
                else:
                    print("Vui lòng nhập đường dẫn!")
                    
            elif choice == "4":
                print("Tạm biệt!")
                break
                
            else:
                print("Lựa chọn không hợp lệ!")
                
        except KeyboardInterrupt:
            print("\nTạm biệt!")
            break
        except Exception as e:
            print(f"Lỗi: {e}")

if __name__ == "__main__":
    main()

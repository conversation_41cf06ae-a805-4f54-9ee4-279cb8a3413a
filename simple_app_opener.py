import os
import glob
import sys

def open_922_s5_proxy():
    """Mở ứng dụng 922 S5 Proxy một cách đơn giản"""
    
    app_folder = r"C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy"
    
    print("🔍 Đang tìm 922 S5 Proxy...")
    
    try:
        if os.path.exists(app_folder):
            print(f"✅ Tìm thấy thư mục: {app_folder}")
            
            # Tìm tất cả file trong thư mục
            all_files = glob.glob(os.path.join(app_folder, "*"))
            
            for file_path in all_files:
                if file_path.endswith(('.lnk', '.exe')):
                    print(f"🚀 Đang mở: {os.path.basename(file_path)}")
                    
                    # Sử dụng os.startfile() - cách tốt nhất trên Windows
                    os.startfile(file_path)
                    print("✅ 922 S5 Proxy đã được mở thành công!")
                    return True
            
            print("❌ Không tìm thấy file thực thi trong thư mục")
        else:
            print(f"❌ Không tìm thấy thư mục: {app_folder}")
            
        # Phương án dự phòng
        print("🔄 Thử phương án dự phòng...")
        os.system('start "922 S5 Proxy"')
        print("✅ Đã thử mở bằng tên ứng dụng!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def open_any_app(app_path_or_name):
    """Mở bất kỳ ứng dụng nào"""
    try:
        if os.path.exists(app_path_or_name):
            # Nếu là đường dẫn file
            print(f"🚀 Đang mở: {app_path_or_name}")
            os.startfile(app_path_or_name)
            print("✅ Ứng dụng đã được mở!")
            return True
        else:
            # Nếu là tên ứng dụng
            print(f"🚀 Đang mở ứng dụng: {app_path_or_name}")
            os.system(f'start "{app_path_or_name}"')
            print("✅ Đã thử mở ứng dụng!")
            return True
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def main():
    print("=" * 40)
    print("    🚀 AUTO APP OPENER")
    print("=" * 40)
    
    while True:
        print("\n📋 Tùy chọn:")
        print("1. Mở 922 S5 Proxy")
        print("2. Mở ứng dụng khác")
        print("3. Thoát")
        
        choice = input("\n👉 Chọn (1-3): ").strip()
        
        if choice == "1":
            open_922_s5_proxy()
            
        elif choice == "2":
            app = input("📝 Nhập tên hoặc đường dẫn ứng dụng: ").strip()
            if app:
                open_any_app(app)
            else:
                print("❌ Vui lòng nhập tên ứng dụng!")
                
        elif choice == "3":
            print("👋 Tạm biệt!")
            break
            
        else:
            print("❌ Lựa chọn không hợp lệ!")

if __name__ == "__main__":
    main()

import os
import subprocess
import glob

def open_922_s5_proxy():
    """Mở ứng dụng 922 S5 Proxy"""
    
    # Đường dẫn thư mục chứa ứng dụng
    app_folder = r"C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy"
    
    print("Đang tìm và mở 922 S5 Proxy...")
    
    try:
        # Kiểm tra xem thư mục có tồn tại không
        if os.path.exists(app_folder):
            print(f"Tìm thấy thư mục: {app_folder}")
            
            # Tìm tất cả file .lnk và .exe trong thư mục
            lnk_files = glob.glob(os.path.join(app_folder, "*.lnk"))
            exe_files = glob.glob(os.path.join(app_folder, "*.exe"))
            
            # Ưu tiên file .lnk trước
            if lnk_files:
                app_file = lnk_files[0]
                print(f"Tìm thấy shortcut: {app_file}")
                # Mở shortcut bằng start command
                subprocess.run(['cmd', '/c', 'start', '', app_file], shell=True)
                print("✅ 922 S5 Proxy đã được mở thành công!")
                return True
                
            elif exe_files:
                app_file = exe_files[0]
                print(f"Tìm thấy executable: {app_file}")
                # Mở file exe trực tiếp
                subprocess.Popen([app_file])
                print("✅ 922 S5 Proxy đã được mở thành công!")
                return True
            else:
                print("❌ Không tìm thấy file thực thi trong thư mục")
        else:
            print(f"❌ Không tìm thấy thư mục: {app_folder}")
        
        # Phương án dự phòng: thử mở bằng tên
        print("Thử phương án dự phòng...")
        subprocess.run(['cmd', '/c', 'start', '922 S5 Proxy'], shell=True)
        print("✅ Đã thử mở 922 S5 Proxy bằng tên!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi mở ứng dụng: {e}")
        return False

if __name__ == "__main__":
    print("=== MỞ 922 S5 PROXY ===")
    open_922_s5_proxy()
    input("\nNhấn Enter để thoát...")

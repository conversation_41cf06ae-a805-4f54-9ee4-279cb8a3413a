import os
import subprocess
import time

def open_922_s5_proxy():
    """Mở 922 S5 Proxy với đường dẫn chính xác"""
    
    # Đường dẫn thực tế của ứng dụng
    app_path = r"C:\Program Files (x86)\922_S5_Proxy\922_S5_Proxy.exe"
    
    print("🚀 Đang mở 922 S5 Proxy...")
    
    try:
        # Kiểm tra file có tồn tại không
        if os.path.exists(app_path):
            print(f"✅ Tìm thấy ứng dụng: {app_path}")
            
            # Mở ứng dụng
            os.startfile(app_path)
            print("🎉 922 S5 Proxy đã được mở thành công!")
            
            # Đợi một chút để ứng dụng khởi động
            time.sleep(2)
            print("✨ Ứng dụng đang khởi động...")
            
            return True
        else:
            print(f"❌ Không tìm thấy file: {app_path}")
            print("💡 Có thể ứng dụng chưa được cài đặt hoặc đã bị di chuyển")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi mở ứng dụng: {e}")
        return False

def check_app_running():
    """Kiểm tra xem 922 S5 Proxy có đang chạy không"""
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq 922_S5_Proxy.exe'], 
                              capture_output=True, text=True)
        if "922_S5_Proxy.exe" in result.stdout:
            print("✅ 922 S5 Proxy đang chạy")
            return True
        else:
            print("❌ 922 S5 Proxy không chạy")
            return False
    except Exception as e:
        print(f"Lỗi khi kiểm tra: {e}")
        return False

def close_922_s5_proxy():
    """Đóng 922 S5 Proxy nếu đang chạy"""
    try:
        result = subprocess.run(['taskkill', '/F', '/IM', '922_S5_Proxy.exe'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Đã đóng 922 S5 Proxy")
            return True
        else:
            print("❌ Không thể đóng 922 S5 Proxy (có thể không đang chạy)")
            return False
    except Exception as e:
        print(f"Lỗi khi đóng ứng dụng: {e}")
        return False

def main():
    print("=" * 50)
    print("    🎯 OPTIMIZED 922 S5 PROXY LAUNCHER")
    print("=" * 50)
    
    while True:
        print("\n📋 Tùy chọn:")
        print("1. 🚀 Mở 922 S5 Proxy")
        print("2. 🔍 Kiểm tra trạng thái")
        print("3. ❌ Đóng 922 S5 Proxy")
        print("4. 🔄 Khởi động lại")
        print("5. 🚪 Thoát")
        
        choice = input("\n👉 Chọn (1-5): ").strip()
        
        if choice == "1":
            print("\n" + "="*30)
            open_922_s5_proxy()
            
        elif choice == "2":
            print("\n" + "="*30)
            check_app_running()
            
        elif choice == "3":
            print("\n" + "="*30)
            close_922_s5_proxy()
            
        elif choice == "4":
            print("\n" + "="*30)
            print("🔄 Đang khởi động lại 922 S5 Proxy...")
            close_922_s5_proxy()
            time.sleep(2)
            open_922_s5_proxy()
            
        elif choice == "5":
            print("\n👋 Tạm biệt!")
            break
            
        else:
            print("❌ Lựa chọn không hợp lệ!")

if __name__ == "__main__":
    main()

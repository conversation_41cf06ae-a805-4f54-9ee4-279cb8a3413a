# 🚀 Auto App Launcher

Bộ công cụ tự động mở ứng dụng trên Windows, đặc biệt được tối ưu cho **922 S5 Proxy**.

## 📁 Các file trong dự án

### 1. `simple_app_opener.py` ⭐ (<PERSON><PERSON><PERSON><PERSON><PERSON> dùng)
- Script Python đơn giản và hiệu quả nhất
- Sử dụng `os.startfile()` - phương pháp tốt nhất trên Windows
- Giao diện menu thân thiện
- <PERSON><PERSON> thể mở bất kỳ ứng dụng nào

### 2. `open_922_s5_proxy.py`
- Script chuyên dụng chỉ để mở 922 S5 Proxy
- Tự động tìm kiếm trong thư mục Start Menu
- Có phương án dự phòng

### 3. `open_922_s5_proxy.bat`
- File batch Windows thuần túy
- <PERSON><PERSON><PERSON>ng cần Python
- Ch<PERSON>y trự<PERSON> tiếp bằng double-click

### 4. `app_launcher.py`
- Script Python đầy đủ tính năng
- <PERSON><PERSON> khả năng tìm kiếm ứng dụng trong nhiều thư mục
- Nhiều tùy chọn mở ứng dụng

## 🚀 Cách sử dụng

### Phương pháp 1: Chạy script Python (Khuyên dùng)
```bash
python simple_app_opener.py
```

### Phương pháp 2: Chạy file batch
- Double-click vào `open_922_s5_proxy.bat`

### Phương pháp 3: Chạy script chuyên dụng
```bash
python open_922_s5_proxy.py
```

## 📋 Tính năng

✅ **Mở 922 S5 Proxy tự động**
- Tìm kiếm trong: `C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy`
- Hỗ trợ cả file `.lnk` và `.exe`
- Có phương án dự phòng nếu không tìm thấy

✅ **Mở ứng dụng bất kỳ**
- Nhập tên ứng dụng (VD: "notepad", "calculator")
- Nhập đường dẫn đầy đủ đến file
- Tự động phát hiện loại file

✅ **Giao diện thân thiện**
- Menu lựa chọn rõ ràng
- Thông báo trạng thái chi tiết
- Emoji để dễ nhìn

## 🔧 Yêu cầu hệ thống

- **Hệ điều hành**: Windows
- **Python**: 3.6+ (cho các script Python)
- **Quyền truy cập**: Đọc thư mục Start Menu

## 🎯 Ví dụ sử dụng

### Mở 922 S5 Proxy
```python
from simple_app_opener import open_922_s5_proxy
open_922_s5_proxy()
```

### Mở ứng dụng khác
```python
from simple_app_opener import open_any_app

# Mở Notepad
open_any_app("notepad")

# Mở Calculator
open_any_app("calc")

# Mở file cụ thể
open_any_app(r"C:\Program Files\SomeApp\app.exe")
```

## 🛠️ Tùy chỉnh

Để thay đổi đường dẫn ứng dụng 922 S5 Proxy, sửa biến `app_folder` trong các script:

```python
app_folder = r"C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy"
```

## 🐛 Xử lý lỗi

Nếu gặp lỗi:

1. **Không tìm thấy ứng dụng**: Kiểm tra đường dẫn có đúng không
2. **Lỗi quyền truy cập**: Chạy với quyền Administrator
3. **Python không hoạt động**: Đảm bảo Python đã được cài đặt

## 📞 Hỗ trợ

Nếu cần hỗ trợ hoặc có góp ý, hãy liên hệ hoặc tạo issue mới!

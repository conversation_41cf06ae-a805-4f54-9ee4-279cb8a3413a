# 🚀 Auto App Launcher

Bộ công cụ tự động mở ứng dụng trên Windows, đặc biệt được tối ưu cho **922 S5 Proxy**.

## 📁 Các file trong dự án

### 1. `quick_launch_922.py` ⭐ (<PERSON><PERSON><PERSON><PERSON><PERSON> dùng nhất)
- Script siêu đơn giản và nhanh chóng
- Sử dụng đường dẫn chính xác: `C:\Program Files (x86)\922_S5_Proxy\922_S5_Proxy.exe`
- Mở ứng dụng ngay lập tức
- Không cần menu, chỉ cần chạy

### 2. `optimized_922_launcher.py` ⭐ (<PERSON><PERSON><PERSON> đủ tính năng)
- Script tối ưu với menu đầy đủ
- <PERSON><PERSON> thể mở, đóng, kiểm tra trạng thái, khởi động lại
- Sử dụng đường dẫn chính xác
- G<PERSON><PERSON> diện thân thiện với emoji

### 3. `open_922_s5_proxy.bat` ⭐ (K<PERSON><PERSON>ng cần Python)
- File batch Windows đã được cập nhật
- <PERSON>ử dụng đường dẫn chính xác
- Chạy trực tiếp bằng double-click

### 4. `advanced_app_finder.py` (Công cụ phân tích)
- Script phân tích chi tiết để tìm đường dẫn thực tế
- Đọc target từ file .lnk
- Tìm kiếm trong Registry và Program Files
- Thử nhiều phương pháp mở ứng dụng

### 5. `simple_app_opener.py` (Đa năng)
- Script có thể mở bất kỳ ứng dụng nào
- Giao diện menu thân thiện
- Tìm kiếm tự động

### 6. `app_launcher.py` (Tìm kiếm nâng cao)
- Script với khả năng tìm kiếm trong nhiều thư mục
- Nhiều tùy chọn mở ứng dụng

## 🚀 Cách sử dụng

### Phương pháp 1: Mở nhanh (Khuyên dùng nhất) ⚡
```bash
python quick_launch_922.py
```

### Phương pháp 2: Menu đầy đủ tính năng 🎛️
```bash
python optimized_922_launcher.py
```

### Phương pháp 3: File batch (Không cần Python) 📁
- Double-click vào `open_922_s5_proxy.bat`

### Phương pháp 4: Phân tích và tìm đường dẫn 🔍
```bash
python advanced_app_finder.py
```

## 🎯 Đường dẫn chính xác đã tìm thấy

**922 S5 Proxy** được cài đặt tại:
```
C:\Program Files (x86)\922_S5_Proxy\922_S5_Proxy.exe
```

Shortcut trong Start Menu:
```
C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy\922 S5 Proxy.lnk
```

## 📋 Tính năng

✅ **Mở 922 S5 Proxy tự động**
- Tìm kiếm trong: `C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy`
- Hỗ trợ cả file `.lnk` và `.exe`
- Có phương án dự phòng nếu không tìm thấy

✅ **Mở ứng dụng bất kỳ**
- Nhập tên ứng dụng (VD: "notepad", "calculator")
- Nhập đường dẫn đầy đủ đến file
- Tự động phát hiện loại file

✅ **Giao diện thân thiện**
- Menu lựa chọn rõ ràng
- Thông báo trạng thái chi tiết
- Emoji để dễ nhìn

## 🔧 Yêu cầu hệ thống

- **Hệ điều hành**: Windows
- **Python**: 3.6+ (cho các script Python)
- **Quyền truy cập**: Đọc thư mục Start Menu

## 🎯 Ví dụ sử dụng

### Mở 922 S5 Proxy
```python
from simple_app_opener import open_922_s5_proxy
open_922_s5_proxy()
```

### Mở ứng dụng khác
```python
from simple_app_opener import open_any_app

# Mở Notepad
open_any_app("notepad")

# Mở Calculator
open_any_app("calc")

# Mở file cụ thể
open_any_app(r"C:\Program Files\SomeApp\app.exe")
```

## 🛠️ Tùy chỉnh

Để thay đổi đường dẫn ứng dụng 922 S5 Proxy, sửa biến `app_folder` trong các script:

```python
app_folder = r"C:\ProgramData\Microsoft\Windows\Start Menu\Programs\922 S5 Proxy"
```

## 🐛 Xử lý lỗi

Nếu gặp lỗi:

1. **Không tìm thấy ứng dụng**: Kiểm tra đường dẫn có đúng không
2. **Lỗi quyền truy cập**: Chạy với quyền Administrator
3. **Python không hoạt động**: Đảm bảo Python đã được cài đặt

## 📞 Hỗ trợ

Nếu cần hỗ trợ hoặc có góp ý, hãy liên hệ hoặc tạo issue mới!

import os

# Đường dẫn chính xác của 922 S5 Proxy
APP_PATH = r"C:\Program Files (x86)\922_S5_Proxy\922_S5_Proxy.exe"

def quick_launch():
    """Mở 922 S5 Proxy nhanh chóng"""
    try:
        if os.path.exists(APP_PATH):
            print("🚀 Đang mở 922 S5 Proxy...")
            os.startfile(APP_PATH)
            print("✅ Thành công!")
        else:
            print("❌ Không tìm thấy ứng dụng!")
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    quick_launch()
    input("Nhấn Enter để thoát...")
